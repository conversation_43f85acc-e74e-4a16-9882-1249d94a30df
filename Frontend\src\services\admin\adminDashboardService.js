import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/dashboard`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Dashboard Statistics
export const getDashboardStats = async () => {
  try {
    const response = await api.get('/stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Recent Activity
export const getRecentActivity = async (limit = 20) => {
  try {
    const response = await api.get('/activity', {
      params: { limit }
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Pending Approvals
export const getPendingApprovals = async () => {
  try {
    const response = await api.get('/pending-approvals');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Analytics Data
export const getAnalytics = async (period = '6m') => {
  try {
    const response = await api.get('/analytics', {
      params: { period }
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Top Performers
export const getTopPerformers = async () => {
  try {
    const response = await api.get('/top-performers');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// System Health
export const getSystemHealth = async () => {
  try {
    const response = await api.get('/system-health');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Combined dashboard data fetch
export const getDashboardData = async () => {
  try {
    const [stats, activity, approvals, analytics] = await Promise.all([
      getDashboardStats(),
      getRecentActivity(10),
      getPendingApprovals(),
      getAnalytics()
    ]);

    return {
      stats: stats.data,
      activity: activity.data,
      approvals: approvals.data,
      analytics: analytics.data
    };
  } catch (error) {
    throw error;
  }
};

export default {
  getDashboardStats,
  getRecentActivity,
  getPendingApprovals,
  getAnalytics,
  getTopPerformers,
  getSystemHealth,
  getDashboardData
};
