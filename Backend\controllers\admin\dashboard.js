const ErrorResponse = require('../../utils/errorResponse');
const User = require('../../models/User');
const Content = require('../../models/Content');
const Order = require('../../models/Order');
const Payment = require('../../models/Payment');
const CustomRequest = require('../../models/CustomRequest');
const Bid = require('../../models/Bid');
const CmsPage = require('../../models/CmsPage');

// @desc    Get comprehensive admin dashboard statistics
// @route   GET /api/admin/dashboard/stats
// @access  Private/Admin
exports.getAdminDashboardStats = async (req, res, next) => {
  try {
    // Get current date for time-based calculations
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const yesterday = new Date(now.setDate(now.getDate() - 1));

    // User statistics
    const totalUsers = await User.countDocuments();
    const totalBuyers = await User.countDocuments({ role: 'buyer' });
    const totalSellers = await User.countDocuments({ role: 'seller' });
    const activeUsers = await User.countDocuments({
      lastLogin: { $gte: startOfWeek },
      status: 'active'
    });
    const newUsersThisMonth = await User.countDocuments({
      createdAt: { $gte: startOfMonth }
    });
    const pendingSellerVerifications = await User.countDocuments({
      role: 'seller',
      isVerified: false
    });

    // Content statistics
    const totalContent = await Content.countDocuments();
    const publishedContent = await Content.countDocuments({ status: 'Published' });
    const pendingContent = await Content.countDocuments({ status: 'Under Review' });
    const draftContent = await Content.countDocuments({ status: 'Draft' });
    const newContentThisMonth = await Content.countDocuments({
      createdAt: { $gte: startOfMonth }
    });

    // Order and sales statistics
    const totalOrders = await Order.countDocuments();
    const completedOrders = await Order.countDocuments({ status: 'Completed' });
    const pendingOrders = await Order.countDocuments({ status: 'Pending' });
    const ordersThisMonth = await Order.countDocuments({
      createdAt: { $gte: startOfMonth }
    });

    // Revenue statistics
    const revenueStats = await Payment.aggregate([
      { $match: { status: 'Completed' } },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          platformFees: { $sum: '$platformFee' },
          sellerEarnings: { $sum: '$sellerEarnings' }
        }
      }
    ]);

    const monthlyRevenue = await Payment.aggregate([
      {
        $match: {
          status: 'Completed',
          createdAt: { $gte: startOfMonth }
        }
      },
      {
        $group: {
          _id: null,
          monthlyRevenue: { $sum: '$amount' },
          monthlyPlatformFees: { $sum: '$platformFee' }
        }
      }
    ]);

    // Bid and auction statistics
    const totalBids = await Bid.countDocuments();
    const activeBids = await Bid.countDocuments({ status: 'Active' });
    const activeAuctions = await Content.countDocuments({
      saleType: { $in: ['Auction', 'Both'] },
      status: 'Published',
      'auctionDetails.endTime': { $gt: new Date() }
    });

    // Custom request statistics
    const totalRequests = await CustomRequest.countDocuments();
    const pendingRequests = await CustomRequest.countDocuments({ status: 'Pending' });

    // CMS statistics
    const totalCMSPages = await CmsPage.countDocuments();
    const publishedCMSPages = await CmsPage.countDocuments({ status: 'published' });

    res.status(200).json({
      success: true,
      data: {
        users: {
          total: totalUsers,
          buyers: totalBuyers,
          sellers: totalSellers,
          active: activeUsers,
          newThisMonth: newUsersThisMonth,
          pendingVerifications: pendingSellerVerifications
        },
        content: {
          total: totalContent,
          published: publishedContent,
          pending: pendingContent,
          draft: draftContent,
          newThisMonth: newContentThisMonth
        },
        orders: {
          total: totalOrders,
          completed: completedOrders,
          pending: pendingOrders,
          thisMonth: ordersThisMonth
        },
        revenue: {
          total: revenueStats[0]?.totalRevenue || 0,
          platformFees: revenueStats[0]?.platformFees || 0,
          sellerEarnings: revenueStats[0]?.sellerEarnings || 0,
          monthly: monthlyRevenue[0]?.monthlyRevenue || 0,
          monthlyPlatformFees: monthlyRevenue[0]?.monthlyPlatformFees || 0
        },
        bids: {
          total: totalBids,
          active: activeBids,
          activeAuctions: activeAuctions
        },
        requests: {
          total: totalRequests,
          pending: pendingRequests
        },
        cms: {
          total: totalCMSPages,
          published: publishedCMSPages
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get recent activity across the platform
// @route   GET /api/admin/dashboard/activity
// @access  Private/Admin
exports.getRecentActivity = async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit) || 20;

    // Get recent orders
    const recentOrders = await Order.find()
      .sort('-createdAt')
      .limit(limit)
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email')
      .populate('content', 'title sport contentType')
      .select('amount status orderType createdAt');

    // Get recent user registrations
    const recentUsers = await User.find()
      .sort('-createdAt')
      .limit(limit)
      .select('firstName lastName email role createdAt');

    // Get recent content uploads
    const recentContent = await Content.find()
      .sort('-createdAt')
      .limit(limit)
      .populate('seller', 'firstName lastName email')
      .select('title sport contentType status createdAt');

    // Get recent bids
    const recentBids = await Bid.find()
      .sort('-createdAt')
      .limit(limit)
      .populate('bidder', 'firstName lastName email')
      .populate('content', 'title')
      .select('amount status isHighestBid createdAt');

    // Combine and format activity
    const activity = [
      ...recentOrders.map(order => ({
        id: order._id,
        type: 'order',
        description: `Order placed for ${order.content?.title}`,
        user: `${order.buyer?.firstName} ${order.buyer?.lastName}`,
        amount: order.amount,
        timestamp: order.createdAt,
        status: order.status
      })),
      ...recentUsers.map(user => ({
        id: user._id,
        type: 'user_registration',
        description: `New ${user.role} registered`,
        user: `${user.firstName} ${user.lastName}`,
        timestamp: user.createdAt
      })),
      ...recentContent.map(content => ({
        id: content._id,
        type: 'content_upload',
        description: `New content uploaded: ${content.title}`,
        user: `${content.seller?.firstName} ${content.seller?.lastName}`,
        timestamp: content.createdAt,
        status: content.status
      })),
      ...recentBids.map(bid => ({
        id: bid._id,
        type: 'bid',
        description: `Bid placed on ${bid.content?.title}`,
        user: `${bid.bidder?.firstName} ${bid.bidder?.lastName}`,
        amount: bid.amount,
        timestamp: bid.createdAt,
        status: bid.status
      }))
    ];

    // Sort by timestamp and limit
    activity.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    const limitedActivity = activity.slice(0, limit);

    res.status(200).json({
      success: true,
      data: limitedActivity
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get pending approvals
// @route   GET /api/admin/dashboard/pending-approvals
// @access  Private/Admin
exports.getPendingApprovals = async (req, res, next) => {
  try {
    // Pending content approvals
    const pendingContent = await Content.find({ status: 'Under Review' })
      .populate('seller', 'firstName lastName email')
      .select('title sport contentType createdAt')
      .sort('-createdAt');

    // Pending seller verifications
    const pendingSellers = await User.find({
      role: 'seller',
      isVerified: false
    })
      .select('firstName lastName email createdAt')
      .sort('-createdAt');

    res.status(200).json({
      success: true,
      data: {
        content: pendingContent.map(content => ({
          id: content._id,
          type: 'content',
          title: content.title,
          seller: `${content.seller?.firstName} ${content.seller?.lastName}`,
          category: content.contentType,
          sport: content.sport,
          submissionDate: content.createdAt
        })),
        sellers: pendingSellers.map(seller => ({
          id: seller._id,
          type: 'seller_verification',
          name: `${seller.firstName} ${seller.lastName}`,
          email: seller.email,
          submissionDate: seller.createdAt
        }))
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get analytics data for charts
// @route   GET /api/admin/dashboard/analytics
// @access  Private/Admin
exports.getAnalytics = async (req, res, next) => {
  try {
    const { period = '6m' } = req.query;

    // Calculate date range based on period
    const now = new Date();
    let startDate;

    switch (period) {
      case '7d':
        startDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case '30d':
        startDate = new Date(now.setDate(now.getDate() - 30));
        break;
      case '6m':
        startDate = new Date(now.setMonth(now.getMonth() - 6));
        break;
      case '1y':
        startDate = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
      default:
        startDate = new Date(now.setMonth(now.getMonth() - 6));
    }

    // Revenue over time
    const revenueData = await Payment.aggregate([
      {
        $match: {
          status: 'Completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            month: { $month: '$createdAt' },
            year: { $year: '$createdAt' }
          },
          revenue: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // User registrations over time
    const userRegistrations = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            month: { $month: '$createdAt' },
            year: { $year: '$createdAt' },
            role: '$role'
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Content by category
    const contentByCategory = await Content.aggregate([
      {
        $group: {
          _id: '$contentType',
          count: { $sum: 1 }
        }
      }
    ]);

    // Revenue by sport
    const revenueBySport = await Payment.aggregate([
      {
        $match: { status: 'Completed' }
      },
      {
        $lookup: {
          from: 'orders',
          localField: 'order',
          foreignField: '_id',
          as: 'orderInfo'
        }
      },
      { $unwind: '$orderInfo' },
      {
        $lookup: {
          from: 'contents',
          localField: 'orderInfo.content',
          foreignField: '_id',
          as: 'contentInfo'
        }
      },
      { $unwind: '$contentInfo' },
      {
        $group: {
          _id: '$contentInfo.sport',
          revenue: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        revenue: revenueData,
        userRegistrations: userRegistrations,
        contentByCategory: contentByCategory,
        revenueBySport: revenueBySport
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get top performers
// @route   GET /api/admin/dashboard/top-performers
// @access  Private/Admin
exports.getTopPerformers = async (req, res, next) => {
  try {
    // Top selling content
    const topContent = await Order.aggregate([
      {
        $group: {
          _id: '$content',
          sales: { $sum: 1 },
          revenue: { $sum: '$amount' }
        }
      },
      { $sort: { sales: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'contents',
          localField: '_id',
          foreignField: '_id',
          as: 'contentInfo'
        }
      },
      { $unwind: '$contentInfo' },
      {
        $project: {
          title: '$contentInfo.title',
          sport: '$contentInfo.sport',
          contentType: '$contentInfo.contentType',
          sales: 1,
          revenue: 1
        }
      }
    ]);

    // Top sellers by revenue
    const topSellers = await Payment.aggregate([
      {
        $match: { status: 'Completed' }
      },
      {
        $group: {
          _id: '$seller',
          revenue: { $sum: '$sellerEarnings' },
          sales: { $sum: 1 }
        }
      },
      { $sort: { revenue: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'sellerInfo'
        }
      },
      { $unwind: '$sellerInfo' },
      {
        $project: {
          name: { $concat: ['$sellerInfo.firstName', ' ', '$sellerInfo.lastName'] },
          email: '$sellerInfo.email',
          revenue: 1,
          sales: 1
        }
      }
    ]);

    // Top buyers by spending
    const topBuyers = await Payment.aggregate([
      {
        $match: { status: 'Completed' }
      },
      {
        $lookup: {
          from: 'orders',
          localField: 'order',
          foreignField: '_id',
          as: 'orderInfo'
        }
      },
      { $unwind: '$orderInfo' },
      {
        $group: {
          _id: '$orderInfo.buyer',
          spent: { $sum: '$amount' },
          purchases: { $sum: 1 }
        }
      },
      { $sort: { spent: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'buyerInfo'
        }
      },
      { $unwind: '$buyerInfo' },
      {
        $project: {
          name: { $concat: ['$buyerInfo.firstName', ' ', '$buyerInfo.lastName'] },
          email: '$buyerInfo.email',
          spent: 1,
          purchases: 1
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        topContent,
        topSellers,
        topBuyers
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get system health metrics
// @route   GET /api/admin/dashboard/system-health
// @access  Private/Admin
exports.getSystemHealth = async (req, res, next) => {
  try {
    // Database connection status
    const dbStatus = require('mongoose').connection.readyState === 1 ? 'connected' : 'disconnected';

    // Get system metrics
    const metrics = {
      database: {
        status: dbStatus,
        collections: {
          users: await User.countDocuments(),
          content: await Content.countDocuments(),
          orders: await Order.countDocuments(),
          payments: await Payment.countDocuments(),
          bids: await Bid.countDocuments()
        }
      },
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        nodeVersion: process.version,
        environment: process.env.NODE_ENV
      },
      errors: {
        // This would typically come from error logging service
        last24Hours: 0,
        criticalErrors: 0
      }
    };

    res.status(200).json({
      success: true,
      data: metrics
    });
  } catch (err) {
    next(err);
  }
};
